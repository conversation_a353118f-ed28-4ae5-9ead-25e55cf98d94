export function useConfig() {
  const globalConfig = {
    // 这里可以预加载一些默认配置
  };

  // _server_niepan_config
  const serverConfig = {
    // 这里可以预加载一些默认配置
  };

  const _global = window as any;

  // 动态从 window 获取
  if (!_global._aa_niepan_global_config) {
    console.warn(
      '请注意，当前未正确挂载 _aa_niepan_global_config，请检查部署环境是否正确部署了 config.js'
    );
    _global._aa_niepan_global_config = {};
  }

  if (!_global._server_niepan_config) {
    console.warn(
      '请注意，当前未正确挂载 _server_niepan_config，请检查部署环境是否正确部署了 config.js'
    );
    _global._server_niepan_config = {};
  }

  Object.assign(globalConfig, _global._aa_niepan_global_config);
  Object.assign(serverConfig, _global._server_niepan_config);

  return {
    globalConfig,
    serverConfig,
  };
}
